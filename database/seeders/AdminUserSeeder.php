<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user if it doesn't exist
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'مدير النظام',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'),
                'role' => 'admin',
                'email_verified_at' => now(),
            ]
        );

        // Create demo user for testing
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'مستخدم تجريبي',
                'email' => '<EMAIL>',
                'password' => Hash::make('demo123'),
                'role' => 'user',
                'email_verified_at' => now(),
            ]
        );
    }
}
