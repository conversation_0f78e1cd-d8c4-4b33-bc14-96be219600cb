// Enhanced Authentication UI Interactions
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all authentication enhancements
    initFloatingLabels();
    initPasswordToggle();
    initFormValidation();
    initLoadingStates();
    initAnimations();
});

// Floating Labels Enhancement
function initFloatingLabels() {
    const inputs = document.querySelectorAll('.floating-label-input');
    
    inputs.forEach(input => {
        // Handle initial state
        if (input.value) {
            input.classList.add('has-value');
        }
        
        // Handle focus/blur events
        input.addEventListener('focus', function() {
            this.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.classList.remove('focused');
            if (this.value) {
                this.classList.add('has-value');
            } else {
                this.classList.remove('has-value');
            }
        });
        
        // Handle input events
        input.addEventListener('input', function() {
            if (this.value) {
                this.classList.add('has-value');
            } else {
                this.classList.remove('has-value');
            }
        });
    });
}

// Password Visibility Toggle
function initPasswordToggle() {
    const toggleButton = document.getElementById('togglePassword');
    const passwordInput = document.getElementById('password');
    const eyeIcon = document.getElementById('eyeIcon');
    const eyeOffIcon = document.getElementById('eyeOffIcon');
    
    if (toggleButton && passwordInput) {
        toggleButton.addEventListener('click', function() {
            const isPassword = passwordInput.type === 'password';
            
            // Toggle input type
            passwordInput.type = isPassword ? 'text' : 'password';
            
            // Toggle icons with smooth transition
            if (isPassword) {
                eyeIcon.style.opacity = '0';
                setTimeout(() => {
                    eyeIcon.classList.add('hidden');
                    eyeOffIcon.classList.remove('hidden');
                    eyeOffIcon.style.opacity = '1';
                }, 150);
            } else {
                eyeOffIcon.style.opacity = '0';
                setTimeout(() => {
                    eyeOffIcon.classList.add('hidden');
                    eyeIcon.classList.remove('hidden');
                    eyeIcon.style.opacity = '1';
                }, 150);
            }
            
            // Add ripple effect
            addRippleEffect(toggleButton);
        });
    }
}

// Form Validation Enhancement
function initFormValidation() {
    const form = document.getElementById('loginForm');
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    
    if (form) {
        // Real-time validation
        emailInput?.addEventListener('blur', validateEmail);
        passwordInput?.addEventListener('blur', validatePassword);
        
        // Form submission validation
        form.addEventListener('submit', function(e) {
            let isValid = true;
            
            if (!validateEmail()) isValid = false;
            if (!validatePassword()) isValid = false;
            
            if (!isValid) {
                e.preventDefault();
                shakeForm();
            }
        });
    }
}

// Email validation
function validateEmail() {
    const emailInput = document.getElementById('email');
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (emailInput && emailInput.value) {
        const isValid = emailRegex.test(emailInput.value);
        toggleInputError(emailInput, !isValid, 'يرجى إدخال بريد إلكتروني صحيح');
        return isValid;
    }
    return true;
}

// Password validation
function validatePassword() {
    const passwordInput = document.getElementById('password');
    
    if (passwordInput && passwordInput.value) {
        const isValid = passwordInput.value.length >= 6;
        toggleInputError(passwordInput, !isValid, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل');
        return isValid;
    }
    return true;
}

// Toggle input error state
function toggleInputError(input, hasError, message) {
    const errorContainer = input.parentNode.nextElementSibling;
    
    if (hasError) {
        input.classList.add('input-error');
        if (errorContainer && !errorContainer.textContent) {
            errorContainer.innerHTML = `
                <div class="flex items-center space-x-2 space-x-reverse text-red-600 text-sm">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>${message}</span>
                </div>
            `;
        }
    } else {
        input.classList.remove('input-error');
        if (errorContainer) {
            errorContainer.innerHTML = '';
        }
    }
}

// Loading States
function initLoadingStates() {
    const form = document.getElementById('loginForm');
    const submitButton = document.getElementById('loginButton');
    const buttonText = document.getElementById('buttonText');
    const loadingSpinner = document.getElementById('loadingSpinner');
    
    if (form && submitButton) {
        form.addEventListener('submit', function() {
            // Disable form elements
            const inputs = form.querySelectorAll('input, button');
            inputs.forEach(input => input.disabled = true);
            
            // Update button state
            submitButton.classList.add('loading');
            buttonText.textContent = 'جاري تسجيل الدخول...';
            loadingSpinner?.classList.remove('hidden');
            
            // Add pulse animation to button
            submitButton.classList.add('pulse-slow');
        });
    }
}

// Animations
function initAnimations() {
    // Stagger animation for form elements
    const formElements = document.querySelectorAll('.space-y-6 > div, .space-y-6 > button');
    formElements.forEach((element, index) => {
        element.style.animationDelay = `${index * 0.1}s`;
        element.classList.add('fade-in');
    });
    
    // Add hover effects to interactive elements
    addHoverEffects();
}

// Add hover effects
function addHoverEffects() {
    const interactiveElements = document.querySelectorAll('button, a, input[type="checkbox"]');
    
    interactiveElements.forEach(element => {
        element.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.02)';
        });
        
        element.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });
}

// Ripple effect for buttons
function addRippleEffect(element) {
    const ripple = document.createElement('span');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    
    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = (event.clientX - rect.left - size / 2) + 'px';
    ripple.style.top = (event.clientY - rect.top - size / 2) + 'px';
    ripple.classList.add('ripple');
    
    element.appendChild(ripple);
    
    setTimeout(() => {
        ripple.remove();
    }, 600);
}

// Shake animation for form errors
function shakeForm() {
    const form = document.getElementById('loginForm');
    if (form) {
        form.style.animation = 'shake 0.5s ease-in-out';
        setTimeout(() => {
            form.style.animation = '';
        }, 500);
    }
}

// Auto-focus on error fields
function focusErrorField() {
    const errorInputs = document.querySelectorAll('.input-error');
    if (errorInputs.length > 0) {
        errorInputs[0].focus();
    }
}

// Keyboard navigation enhancement
document.addEventListener('keydown', function(e) {
    // Enter key on remember me checkbox
    if (e.key === 'Enter' && e.target.type === 'checkbox') {
        e.target.checked = !e.target.checked;
    }
    
    // Escape key to clear form
    if (e.key === 'Escape') {
        const form = document.getElementById('loginForm');
        if (form && confirm('هل تريد مسح النموذج؟')) {
            form.reset();
            const inputs = form.querySelectorAll('.floating-label-input');
            inputs.forEach(input => {
                input.classList.remove('has-value', 'focused');
            });
        }
    }
});

// Add CSS for additional animations
const additionalStyles = `
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
        20%, 40%, 60%, 80% { transform: translateX(5px); }
    }
    
    .loading {
        pointer-events: none;
    }
`;

// Inject additional styles
const styleSheet = document.createElement('style');
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);
