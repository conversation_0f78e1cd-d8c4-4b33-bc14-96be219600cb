@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    html {
        direction: rtl;
        font-family: 'IBM Plex Sans Arabic', sans-serif;
    }

    body {
        @apply font-arabic text-secondary-800 bg-gray-50;
    }

    /* RTL specific adjustments */
    .rtl-flip {
        transform: scaleX(-1);
    }

    /* Custom scrollbar for RTL */
    ::-webkit-scrollbar {
        width: 8px;
    }

    ::-webkit-scrollbar-track {
        @apply bg-gray-100;
    }

    ::-webkit-scrollbar-thumb {
        @apply bg-gray-300 rounded-full;
    }

    ::-webkit-scrollbar-thumb:hover {
        @apply bg-gray-400;
    }
}

@layer components {
    /* Admin Panel Components */
    .admin-card {
        @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6;
    }

    .admin-button {
        @apply inline-flex items-center px-4 py-2 bg-primary-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-primary-700 focus:bg-primary-700 active:bg-primary-900 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition ease-in-out duration-150;
    }

    .admin-button-secondary {
        @apply inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-25 transition ease-in-out duration-150;
    }

    .admin-input {
        @apply border-gray-300 focus:border-primary-500 focus:ring-primary-500 rounded-md shadow-sm;
    }

    .admin-select {
        @apply border-gray-300 focus:border-primary-500 focus:ring-primary-500 rounded-md shadow-sm;
    }

    .admin-textarea {
        @apply border-gray-300 focus:border-primary-500 focus:ring-primary-500 rounded-md shadow-sm;
    }

    .sidebar-link {
        @apply flex items-center px-4 py-3 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors duration-200;
    }

    .sidebar-link.active {
        @apply text-primary-600 bg-primary-50 border-r-2 border-primary-600;
    }

    .status-badge {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
    }

    .status-active {
        @apply bg-green-100 text-green-800;
    }

    .status-inactive {
        @apply bg-red-100 text-red-800;
    }

    .status-pending {
        @apply bg-yellow-100 text-yellow-800;
    }

    .status-completed {
        @apply bg-blue-100 text-blue-800;
    }

    /* Logo Animations */
    .logo-glow {
        animation: glow 2s ease-in-out infinite alternate;
    }

    .logo-rotate {
        animation: rotate 3s linear infinite;
    }

    .logo-float {
        animation: float 3s ease-in-out infinite;
    }

    /* Modern Login Form Styles */
    .login-card {
        @apply bg-white rounded-2xl shadow-xl border border-gray-100 backdrop-blur-sm;
        background: rgba(255, 255, 255, 0.95);
    }

    .floating-label-input {
        @apply w-full px-4 py-4 border-2 border-gray-200 rounded-xl transition-all duration-300 bg-gray-50 text-gray-900;
    }

    .floating-label-input:focus {
        @apply border-primary-500 ring-0 bg-white;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .floating-label {
        @apply absolute right-4 top-4 text-gray-500 transition-all duration-300 pointer-events-none;
    }

    .floating-label-input:focus + .floating-label,
    .floating-label-input:not(:placeholder-shown) + .floating-label {
        @apply top-1 text-sm text-primary-600;
    }

    /* Enhanced Button Styles */
    .btn-primary {
        @apply w-full bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg focus:outline-none focus:ring-4 focus:ring-primary-300;
    }

    .btn-primary:disabled {
        @apply opacity-50 cursor-not-allowed transform-none;
    }

    /* Input Error States */
    .input-error {
        @apply border-red-300 focus:border-red-500;
    }

    .input-error:focus {
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
    }

    /* Checkbox Styling */
    .custom-checkbox {
        @apply w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 focus:ring-2 transition-all duration-200;
    }

    /* Link Hover Effects */
    .link-hover {
        @apply transition-colors duration-200 hover:underline;
    }

    /* Background Animations */
    .bg-animated {
        background: linear-gradient(-45deg, #3b82f6, #1d4ed8, #1e40af, #2563eb);
        background-size: 400% 400%;
        animation: gradientShift 15s ease infinite;
    }

    @keyframes gradientShift {
        0% {
            background-position: 0% 50%;
        }
        50% {
            background-position: 100% 50%;
        }
        100% {
            background-position: 0% 50%;
        }
    }

    /* Pulse Animation for Loading */
    .pulse-slow {
        animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }

    /* Slide In Animation */
    .slide-in-right {
        animation: slideInRight 0.6s ease-out;
    }

    .slide-in-left {
        animation: slideInLeft 0.6s ease-out;
    }

    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideInLeft {
        from {
            transform: translateX(-100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    /* Fade In Animation */
    .fade-in {
        animation: fadeIn 0.8s ease-out;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Existing Keyframes */
    @keyframes glow {
        from {
            box-shadow: 0 0 5px rgba(59, 130, 246, 0.5), 0 0 10px rgba(59, 130, 246, 0.3);
        }
        to {
            box-shadow: 0 0 10px rgba(59, 130, 246, 0.8), 0 0 20px rgba(59, 130, 246, 0.5), 0 0 30px rgba(59, 130, 246, 0.3);
        }
    }

    @keyframes rotate {
        from {
            transform: rotate(0deg);
        }
        to {
            transform: rotate(360deg);
        }
    }

    @keyframes float {
        0%, 100% {
            transform: translateY(0px);
        }
        50% {
            transform: translateY(-10px);
        }
    }

    /* Responsive Design Enhancements */
    @media (max-width: 768px) {
        .login-card {
            @apply mx-4 p-6;
        }

        .floating-label-input {
            @apply py-3 px-3;
        }

        .floating-label {
            @apply right-3 top-3;
        }

        .floating-label-input:focus + .floating-label,
        .floating-label-input:not(:placeholder-shown) + .floating-label {
            @apply top-0.5 text-xs;
        }
    }

    /* Dark Mode Support (Future Enhancement) */
    @media (prefers-color-scheme: dark) {
        .login-card {
            @apply bg-gray-800 border-gray-700;
            background: rgba(31, 41, 55, 0.95);
        }

        .floating-label-input {
            @apply bg-gray-700 border-gray-600 text-white;
        }

        .floating-label-input:focus {
            @apply border-primary-400 bg-gray-600;
        }

        .floating-label {
            @apply text-gray-400;
        }

        .floating-label-input:focus + .floating-label,
        .floating-label-input:not(:placeholder-shown) + .floating-label {
            @apply text-primary-400;
        }
    }
}
