@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    html {
        direction: rtl;
        font-family: 'IBM Plex Sans Arabic', sans-serif;
    }

    body {
        @apply font-arabic text-secondary-800 bg-gray-50;
    }

    /* RTL specific adjustments */
    .rtl-flip {
        transform: scaleX(-1);
    }

    /* Custom scrollbar for RTL */
    ::-webkit-scrollbar {
        width: 8px;
    }

    ::-webkit-scrollbar-track {
        @apply bg-gray-100;
    }

    ::-webkit-scrollbar-thumb {
        @apply bg-gray-300 rounded-full;
    }

    ::-webkit-scrollbar-thumb:hover {
        @apply bg-gray-400;
    }
}

@layer components {
    /* Admin Panel Components */
    .admin-card {
        @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6;
    }

    .admin-button {
        @apply inline-flex items-center px-4 py-2 bg-primary-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-primary-700 focus:bg-primary-700 active:bg-primary-900 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition ease-in-out duration-150;
    }

    .admin-button-secondary {
        @apply inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-25 transition ease-in-out duration-150;
    }

    .admin-input {
        @apply border-gray-300 focus:border-primary-500 focus:ring-primary-500 rounded-md shadow-sm;
    }

    .admin-select {
        @apply border-gray-300 focus:border-primary-500 focus:ring-primary-500 rounded-md shadow-sm;
    }

    .admin-textarea {
        @apply border-gray-300 focus:border-primary-500 focus:ring-primary-500 rounded-md shadow-sm;
    }

    .sidebar-link {
        @apply flex items-center px-4 py-3 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors duration-200;
    }

    .sidebar-link.active {
        @apply text-primary-600 bg-primary-50 border-r-2 border-primary-600;
    }

    .status-badge {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
    }

    .status-active {
        @apply bg-green-100 text-green-800;
    }

    .status-inactive {
        @apply bg-red-100 text-red-800;
    }

    .status-pending {
        @apply bg-yellow-100 text-yellow-800;
    }

    .status-completed {
        @apply bg-blue-100 text-blue-800;
    }

    /* Logo Animations */
    .logo-glow {
        animation: glow 2s ease-in-out infinite alternate;
    }

    .logo-rotate {
        animation: rotate 3s linear infinite;
    }

    .logo-float {
        animation: float 3s ease-in-out infinite;
    }

    @keyframes glow {
        from {
            box-shadow: 0 0 5px rgba(59, 130, 246, 0.5), 0 0 10px rgba(59, 130, 246, 0.3);
        }
        to {
            box-shadow: 0 0 10px rgba(59, 130, 246, 0.8), 0 0 20px rgba(59, 130, 246, 0.5), 0 0 30px rgba(59, 130, 246, 0.3);
        }
    }

    @keyframes rotate {
        from {
            transform: rotate(0deg);
        }
        to {
            transform: rotate(360deg);
        }
    }

    @keyframes float {
        0%, 100% {
            transform: translateY(0px);
        }
        50% {
            transform: translateY(-10px);
        }
    }
}
