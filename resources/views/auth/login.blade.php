<x-guest-layout>
    <!-- Header -->
    <div class="text-center mb-8 fade-in">
        <div class="hidden lg:block">
            <x-application-logo class="w-12 h-12 mx-auto text-primary-600 mb-4 logo-glow" />
        </div>
        <h2 class="text-3xl font-bold text-gray-900 mb-2">{{ __('تسجيل الدخول') }}</h2>
        <p class="text-gray-600">{{ __('مرحباً بك مرة أخرى! يرجى تسجيل الدخول إلى حسابك') }}</p>
    </div>

    <!-- Session Status -->
    <x-auth-session-status class="mb-6" :status="session('status')" />

    <form method="POST" action="{{ route('login') }}" class="space-y-6" id="loginForm">
        @csrf

        <!-- Email Address -->
        <div class="space-y-2">
            <div class="relative">
                <x-text-input id="email" name="email" type="email" :value="old('email')" required autofocus
                    autocomplete="username" placeholder=" "
                    class="peer w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:border-primary-500 focus:ring-0 transition-all duration-300 placeholder-transparent bg-gray-50 focus:bg-white text-gray-900 {{ $errors->get('email') ? 'border-red-300 focus:border-red-500' : '' }}" />
                <label for="email"
                    class="absolute right-4 top-4 text-gray-500 transition-all duration-300 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400 peer-placeholder-shown:top-4 peer-focus:top-1 peer-focus:text-sm peer-focus:text-primary-600 peer-[:not(:placeholder-shown)]:top-1 peer-[:not(:placeholder-shown)]:text-sm peer-[:not(:placeholder-shown)]:text-primary-600">
                    {{ __('البريد الإلكتروني') }}
                </label>
                <div class="absolute left-4 top-4 text-gray-400">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207">
                        </path>
                    </svg>
                </div>
            </div>
            <x-input-error :messages="$errors->get('email')" class="text-sm text-red-600" />
        </div>

        <!-- Password -->
        <div class="space-y-2">
            <div class="relative">
                <x-text-input id="password" name="password" type="password" required autocomplete="current-password"
                    placeholder=" "
                    class="peer w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:border-primary-500 focus:ring-0 transition-all duration-300 placeholder-transparent bg-gray-50 focus:bg-white text-gray-900 {{ $errors->get('password') ? 'border-red-300 focus:border-red-500' : '' }}" />
                <label for="password"
                    class="absolute right-4 top-4 text-gray-500 transition-all duration-300 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400 peer-placeholder-shown:top-4 peer-focus:top-1 peer-focus:text-sm peer-focus:text-primary-600 peer-[:not(:placeholder-shown)]:top-1 peer-[:not(:placeholder-shown)]:text-sm peer-[:not(:placeholder-shown)]:text-primary-600">
                    {{ __('كلمة المرور') }}
                </label>
                <button type="button" id="togglePassword"
                    class="absolute left-4 top-4 text-gray-400 hover:text-gray-600 transition-colors duration-200">
                    <svg id="eyeIcon" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                        </path>
                    </svg>
                    <svg id="eyeOffIcon" class="w-5 h-5 hidden" fill="none" stroke="currentColor"
                        viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21">
                        </path>
                    </svg>
                </button>
            </div>
            <x-input-error :messages="$errors->get('password')" class="text-sm text-red-600" />
        </div>

        <!-- Remember Me & Forgot Password -->
        <div class="flex items-center justify-between">
            <label for="remember_me" class="flex items-center cursor-pointer group">
                <input id="remember_me" type="checkbox" name="remember"
                    class="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 focus:ring-2 transition-all duration-200">
                <span class="mr-2 text-sm text-gray-600 group-hover:text-gray-800 transition-colors duration-200">
                    {{ __('تذكرني') }}
                </span>
            </label>

            @if (Route::has('password.request'))
                <a href="{{ route('password.request') }}"
                    class="text-sm text-primary-600 hover:text-primary-800 font-medium transition-colors duration-200 hover:underline">
                    {{ __('نسيت كلمة المرور؟') }}
                </a>
            @endif
        </div>

        <!-- Login Button -->
        <button type="submit"
            class="w-full bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg focus:outline-none focus:ring-4 focus:ring-primary-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            id="loginButton">
            <span id="buttonText">{{ __('تسجيل الدخول') }}</span>
            <svg id="loadingSpinner" class="hidden animate-spin -ml-1 mr-3 h-5 w-5 text-white inline"
                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
                </circle>
                <path class="opacity-75" fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                </path>
            </svg>
        </button>

        <!-- Register Link -->
        <div class="text-center mt-6">
            <p class="text-sm text-gray-600">
                {{ __('ليس لديك حساب؟') }}
                @if (Route::has('register'))
                    <a href="{{ route('register') }}"
                        class="font-medium text-primary-600 hover:text-primary-800 transition-colors duration-200 hover:underline">
                        {{ __('إنشاء حساب جديد') }}
                    </a>
                @endif
            </p>
        </div>
    </form>

    <!-- JavaScript for Enhanced UX with Comprehensive Debugging -->
    <script>
        // Global debugging flag
        const DEBUG_MODE = true;

        function debugLog(message, data = null) {
            if (DEBUG_MODE) {
                console.log(`[LOGIN DEBUG] ${message}`, data || '');
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            debugLog('DOM Content Loaded - Initializing login functionality');

            // DOM Element Verification
            const loginForm = document.getElementById('loginForm');
            const emailInput = document.getElementById('email');
            const passwordInputField = document.getElementById('password');
            const loginButton = document.getElementById('loginButton');
            const buttonText = document.getElementById('buttonText');
            const loadingSpinner = document.getElementById('loadingSpinner');
            const togglePassword = document.getElementById('togglePassword');
            const eyeIcon = document.getElementById('eyeIcon');
            const eyeOffIcon = document.getElementById('eyeOffIcon');

            // Verify all critical elements exist
            debugLog('Element verification:', {
                loginForm: !!loginForm,
                emailInput: !!emailInput,
                passwordInputField: !!passwordInputField,
                loginButton: !!loginButton,
                buttonText: !!buttonText,
                loadingSpinner: !!loadingSpinner,
                togglePassword: !!togglePassword
            });

            if (!loginForm) {
                console.error('[LOGIN ERROR] Login form not found!');
                return;
            }

            if (!emailInput || !passwordInputField) {
                console.error('[LOGIN ERROR] Email or password input not found!');
                return;
            }

            // Password visibility toggle
            if (togglePassword && passwordInputField && eyeIcon && eyeOffIcon) {
                togglePassword.addEventListener('click', function() {
                    debugLog('Password toggle clicked');
                    const type = passwordInputField.getAttribute('type') === 'password' ? 'text' :
                        'password';
                    passwordInputField.setAttribute('type', type);

                    eyeIcon.classList.toggle('hidden');
                    eyeOffIcon.classList.toggle('hidden');
                });
            }

            // Form submission with enhanced debugging and fetch API
            if (loginForm) {
                loginForm.addEventListener('submit', async function(e) {
                    e.preventDefault(); // Prevent default form submission
                    debugLog('Form submission initiated (using fetch API)');

                    // Show loading state immediately
                    if (loginButton && buttonText && loadingSpinner) {
                        loginButton.disabled = true;
                        buttonText.textContent = '{{ __('جاري تسجيل الدخول...') }}';
                        loadingSpinner.classList.remove('hidden');
                    }

                    try {
                        // CSRF Token Debugging and Verification
                        const csrfToken = document.querySelector('input[name="_token"]');
                        const metaCsrfToken = document.querySelector('meta[name="csrf-token"]');

                        debugLog('CSRF Token Sources:', {
                            formToken: csrfToken ? csrfToken.value.substring(0, 10) + '...' :
                                'NOT FOUND',
                            metaToken: metaCsrfToken ? metaCsrfToken.getAttribute('content')
                                .substring(0, 10) + '...' : 'NOT FOUND'
                        });

                        let tokenValue = null;
                        if (csrfToken) {
                            tokenValue = csrfToken.value;
                            debugLog('Using CSRF Token from form:', tokenValue.substring(0, 10) +
                            '...');
                        } else if (metaCsrfToken) {
                            tokenValue = metaCsrfToken.getAttribute('content');
                            debugLog('Using CSRF Token from meta tag:', tokenValue.substring(0, 10) +
                                '...');
                        } else {
                            throw new Error('No CSRF token found anywhere!');
                        }

                        // Prepare form data
                        const formData = new FormData();
                        formData.append('_token', tokenValue);
                        formData.append('email', emailInput.value);
                        formData.append('password', passwordInputField.value);

                        // Add remember me if checked
                        const rememberCheckbox = document.getElementById('remember_me');
                        if (rememberCheckbox && rememberCheckbox.checked) {
                            formData.append('remember', 'on');
                        }

                        debugLog('Form data being submitted:', {
                            email: emailInput.value,
                            password: passwordInputField.value ? '***' : 'MISSING',
                            _token: tokenValue.substring(0, 10) + '...',
                            remember: rememberCheckbox && rememberCheckbox.checked ? 'on' :
                                'not set'
                        });

                        debugLog('Submitting to:', loginForm.action);
                        debugLog('Document cookies:', document.cookie);

                        // Submit using fetch API
                        const response = await fetch(loginForm.action, {
                            method: 'POST',
                            body: formData,
                            credentials: 'include',
                            redirect: 'manual' // Handle redirects manually
                        });

                        debugLog('Response received:', {
                            status: response.status,
                            statusText: response.statusText,
                            headers: Object.fromEntries(response.headers.entries())
                        });

                        if (response.status === 302) {
                            // Successful login - redirect
                            const location = response.headers.get('Location');
                            debugLog('Login successful! Redirecting to:', location);
                            window.location.href = location || '/dashboard';
                        } else if (response.status === 419) {
                            throw new Error('419 Page Expired - CSRF token validation failed');
                        } else if (response.status === 422) {
                            // Validation errors
                            const errorData = await response.json();
                            debugLog('Validation errors:', errorData);
                            throw new Error('Validation failed: ' + JSON.stringify(errorData.errors));
                        } else {
                            // Other errors
                            const responseText = await response.text();
                            debugLog('Unexpected response body:', responseText.substring(0, 200));
                            throw new Error(
                                `Unexpected response: ${response.status} ${response.statusText}`);
                        }

                    } catch (error) {
                        console.error('[LOGIN ERROR]', error);
                        debugLog('Login failed with error:', error.message);

                        // Reset form state
                        if (loginButton && buttonText && loadingSpinner) {
                            loginButton.disabled = false;
                            buttonText.textContent = '{{ __('تسجيل الدخول') }}';
                            loadingSpinner.classList.add('hidden');
                        }

                        // Show user-friendly error
                        alert('حدث خطأ في تسجيل الدخول: ' + error.message);
                    }
                });
            }

            // Auto-focus on error fields
            @if ($errors->has('email'))
                if (emailInput) {
                    emailInput.focus();
                    debugLog('Auto-focused on email field due to error');
                }
            @elseif ($errors->has('password'))
                if (passwordInputField) {
                    passwordInputField.focus();
                    debugLog('Auto-focused on password field due to error');
                }
            @endif

            debugLog('Login functionality initialization complete');
        });

        // Global error handler
        window.addEventListener('error', function(e) {
            console.error('[GLOBAL ERROR]', e.error);
        });

        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', function(e) {
            console.error('[UNHANDLED PROMISE REJECTION]', e.reason);
        });
    </script>
</x-guest-layout>
