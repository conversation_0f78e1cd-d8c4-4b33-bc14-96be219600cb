<x-guest-layout>
    <!-- Header -->
    <div class="text-center mb-8 fade-in">
        <div class="hidden lg:block">
            <x-application-logo class="w-12 h-12 mx-auto text-primary-600 mb-4 logo-glow" />
        </div>
        <h2 class="text-3xl font-bold text-gray-900 mb-2">{{ __('تسجيل الدخول') }}</h2>
        <p class="text-gray-600">{{ __('مرحباً بك مرة أخرى! يرجى تسجيل الدخول إلى حسابك') }}</p>
    </div>

    <!-- Session Status -->
    <x-auth-session-status class="mb-6" :status="session('status')" />

    <form method="POST" action="{{ route('login') }}" class="space-y-6" id="loginForm">
        @csrf

        <!-- Email Address -->
        <div class="space-y-2">
            <div class="relative">
                <x-text-input id="email" name="email" type="email" :value="old('email')" required autofocus
                    autocomplete="username" placeholder=" "
                    class="peer w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:border-primary-500 focus:ring-0 transition-all duration-300 placeholder-transparent bg-gray-50 focus:bg-white text-gray-900 {{ $errors->get('email') ? 'border-red-300 focus:border-red-500' : '' }}" />
                <label for="email"
                    class="absolute right-4 top-4 text-gray-500 transition-all duration-300 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400 peer-placeholder-shown:top-4 peer-focus:top-1 peer-focus:text-sm peer-focus:text-primary-600 peer-[:not(:placeholder-shown)]:top-1 peer-[:not(:placeholder-shown)]:text-sm peer-[:not(:placeholder-shown)]:text-primary-600">
                    {{ __('البريد الإلكتروني') }}
                </label>
                <div class="absolute left-4 top-4 text-gray-400">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207">
                        </path>
                    </svg>
                </div>
            </div>
            <x-input-error :messages="$errors->get('email')" class="text-sm text-red-600" />
        </div>

        <!-- Password -->
        <div class="space-y-2">
            <div class="relative">
                <x-text-input id="password" name="password" type="password" required autocomplete="current-password"
                    placeholder=" "
                    class="peer w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:border-primary-500 focus:ring-0 transition-all duration-300 placeholder-transparent bg-gray-50 focus:bg-white text-gray-900 {{ $errors->get('password') ? 'border-red-300 focus:border-red-500' : '' }}" />
                <label for="password"
                    class="absolute right-4 top-4 text-gray-500 transition-all duration-300 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400 peer-placeholder-shown:top-4 peer-focus:top-1 peer-focus:text-sm peer-focus:text-primary-600 peer-[:not(:placeholder-shown)]:top-1 peer-[:not(:placeholder-shown)]:text-sm peer-[:not(:placeholder-shown)]:text-primary-600">
                    {{ __('كلمة المرور') }}
                </label>
                <button type="button" id="togglePassword"
                    class="absolute left-4 top-4 text-gray-400 hover:text-gray-600 transition-colors duration-200">
                    <svg id="eyeIcon" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                        </path>
                    </svg>
                    <svg id="eyeOffIcon" class="w-5 h-5 hidden" fill="none" stroke="currentColor"
                        viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21">
                        </path>
                    </svg>
                </button>
            </div>
            <x-input-error :messages="$errors->get('password')" class="text-sm text-red-600" />
        </div>

        <!-- Remember Me & Forgot Password -->
        <div class="flex items-center justify-between">
            <label for="remember_me" class="flex items-center cursor-pointer group">
                <input id="remember_me" type="checkbox" name="remember"
                    class="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 focus:ring-2 transition-all duration-200">
                <span class="mr-2 text-sm text-gray-600 group-hover:text-gray-800 transition-colors duration-200">
                    {{ __('تذكرني') }}
                </span>
            </label>

            @if (Route::has('password.request'))
                <a href="{{ route('password.request') }}"
                    class="text-sm text-primary-600 hover:text-primary-800 font-medium transition-colors duration-200 hover:underline">
                    {{ __('نسيت كلمة المرور؟') }}
                </a>
            @endif
        </div>

        <!-- Login Button -->
        <button type="submit"
            class="w-full bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg focus:outline-none focus:ring-4 focus:ring-primary-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            id="loginButton">
            <span id="buttonText">{{ __('تسجيل الدخول') }}</span>
            <svg id="loadingSpinner" class="hidden animate-spin -ml-1 mr-3 h-5 w-5 text-white inline"
                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
                </circle>
                <path class="opacity-75" fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                </path>
            </svg>
        </button>

        <!-- Divider -->
        <div class="relative my-6">
            <div class="absolute inset-0 flex items-center">
                <div class="w-full border-t border-gray-200"></div>
            </div>
            <div class="relative flex justify-center text-sm">
                <span class="px-4 bg-white text-gray-500">{{ __('أو') }}</span>
            </div>
        </div>

        <!-- Demo Admin Login -->
        <div class="space-y-3">
            <button type="button" id="demoAdminLogin"
                class="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg focus:outline-none focus:ring-4 focus:ring-green-300">
                <div class="flex items-center justify-center space-x-2 space-x-reverse">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    <span>{{ __('دخول المدير التجريبي') }}</span>
                </div>
            </button>

            <button type="button" id="demoUserLogin"
                class="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg focus:outline-none focus:ring-4 focus:ring-blue-300">
                <div class="flex items-center justify-center space-x-2 space-x-reverse">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z">
                        </path>
                    </svg>
                    <span>{{ __('دخول المستخدم التجريبي') }}</span>
                </div>
            </button>
        </div>

        <!-- Register Link -->
        <div class="text-center mt-6">
            <p class="text-sm text-gray-600">
                {{ __('ليس لديك حساب؟') }}
                @if (Route::has('register'))
                    <a href="{{ route('register') }}"
                        class="font-medium text-primary-600 hover:text-primary-800 transition-colors duration-200 hover:underline">
                        {{ __('إنشاء حساب جديد') }}
                    </a>
                @endif
            </p>
        </div>
    </form>

    <!-- JavaScript for Enhanced UX with Comprehensive Debugging -->
    <script>
        // Global debugging flag
        const DEBUG_MODE = true;

        function debugLog(message, data = null) {
            if (DEBUG_MODE) {
                console.log(`[LOGIN DEBUG] ${message}`, data || '');
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            debugLog('DOM Content Loaded - Initializing login functionality');

            // DOM Element Verification
            const loginForm = document.getElementById('loginForm');
            const emailInput = document.getElementById('email');
            const passwordInputField = document.getElementById('password');
            const loginButton = document.getElementById('loginButton');
            const buttonText = document.getElementById('buttonText');
            const loadingSpinner = document.getElementById('loadingSpinner');
            const demoAdminButton = document.getElementById('demoAdminLogin');
            const demoUserButton = document.getElementById('demoUserLogin');
            const togglePassword = document.getElementById('togglePassword');
            const eyeIcon = document.getElementById('eyeIcon');
            const eyeOffIcon = document.getElementById('eyeOffIcon');

            // Verify all critical elements exist
            debugLog('Element verification:', {
                loginForm: !!loginForm,
                emailInput: !!emailInput,
                passwordInputField: !!passwordInputField,
                loginButton: !!loginButton,
                buttonText: !!buttonText,
                loadingSpinner: !!loadingSpinner,
                demoAdminButton: !!demoAdminButton,
                demoUserButton: !!demoUserButton,
                togglePassword: !!togglePassword
            });

            if (!loginForm) {
                console.error('[LOGIN ERROR] Login form not found!');
                return;
            }

            if (!emailInput || !passwordInputField) {
                console.error('[LOGIN ERROR] Email or password input not found!');
                return;
            }

            // Password visibility toggle
            if (togglePassword && passwordInputField && eyeIcon && eyeOffIcon) {
                togglePassword.addEventListener('click', function() {
                    debugLog('Password toggle clicked');
                    const type = passwordInputField.getAttribute('type') === 'password' ? 'text' :
                        'password';
                    passwordInputField.setAttribute('type', type);

                    eyeIcon.classList.toggle('hidden');
                    eyeOffIcon.classList.toggle('hidden');
                });
            }

            // Form submission with loading state
            if (loginForm) {
                loginForm.addEventListener('submit', function(e) {
                    debugLog('Form submission initiated');

                    if (loginButton && buttonText && loadingSpinner) {
                        loginButton.disabled = true;
                        buttonText.textContent = '{{ __('جاري تسجيل الدخول...') }}';
                        loadingSpinner.classList.remove('hidden');
                    }
                });
            }

            // Auto-focus on error fields
            @if ($errors->has('email'))
                if (emailInput) {
                    emailInput.focus();
                    debugLog('Auto-focused on email field due to error');
                }
            @elseif ($errors->has('password'))
                if (passwordInputField) {
                    passwordInputField.focus();
                    debugLog('Auto-focused on password field due to error');
                }
            @endif

            // Demo login functionality with comprehensive debugging
            function fillAndSubmitLogin(email, password, buttonType) {
                debugLog(`Demo login initiated for ${buttonType}`, {
                    email,
                    password: '***'
                });

                try {
                    // Verify form elements exist
                    if (!loginForm) {
                        throw new Error('Login form not found');
                    }
                    if (!emailInput) {
                        throw new Error('Email input not found');
                    }
                    if (!passwordInputField) {
                        throw new Error('Password input not found');
                    }

                    // Disable demo buttons to prevent double-click
                    const demoButtons = document.querySelectorAll('#demoAdminLogin, #demoUserLogin');
                    debugLog(`Found ${demoButtons.length} demo buttons to disable`);

                    demoButtons.forEach((btn, index) => {
                        btn.disabled = true;
                        btn.style.opacity = '0.7';
                        debugLog(`Disabled demo button ${index + 1}`);
                    });

                    // Fill the form fields
                    debugLog('Filling form fields...');
                    emailInput.value = email;
                    passwordInputField.value = password;

                    debugLog('Form fields filled:', {
                        emailValue: emailInput.value,
                        passwordLength: passwordInputField.value.length
                    });

                    // Trigger the floating label animations
                    emailInput.classList.add('has-value');
                    passwordInputField.classList.add('has-value');
                    debugLog('Added has-value classes for floating labels');

                    // Trigger input events to ensure proper state
                    const inputEvent = new Event('input', {
                        bubbles: true
                    });
                    const changeEvent = new Event('change', {
                        bubbles: true
                    });

                    emailInput.dispatchEvent(inputEvent);
                    emailInput.dispatchEvent(changeEvent);
                    passwordInputField.dispatchEvent(inputEvent);
                    passwordInputField.dispatchEvent(changeEvent);
                    debugLog('Dispatched input and change events');

                    // Visual feedback
                    emailInput.style.borderColor = '#10b981';
                    passwordInputField.style.borderColor = '#10b981';
                    debugLog('Applied green border visual feedback');

                    // Verify CSRF token exists
                    const csrfToken = document.querySelector('input[name="_token"]');
                    if (!csrfToken) {
                        throw new Error('CSRF token not found');
                    }
                    debugLog('CSRF token verified:', csrfToken.value.substring(0, 10) + '...');

                    // Add a delay for visual feedback then submit
                    debugLog('Starting 500ms delay before form submission...');
                    setTimeout(() => {
                        try {
                            debugLog('Timeout completed, preparing form submission');

                            // Show loading state on login button
                            if (loginButton && buttonText && loadingSpinner) {
                                loginButton.disabled = true;
                                buttonText.textContent = 'جاري تسجيل الدخول...';
                                loadingSpinner.classList.remove('hidden');
                                debugLog('Updated login button to loading state');
                            }

                            // Final verification before submission
                            debugLog('Final form state before submission:', {
                                action: loginForm.action,
                                method: loginForm.method,
                                emailValue: emailInput.value,
                                passwordLength: passwordInputField.value.length,
                                csrfToken: csrfToken.value.substring(0, 10) + '...'
                            });

                            // Trigger form submission
                            debugLog('Submitting form...');
                            loginForm.submit();
                            debugLog('Form.submit() called successfully');

                        } catch (submitError) {
                            console.error('[LOGIN ERROR] Form submission failed:', submitError);

                            // Re-enable buttons on error
                            demoButtons.forEach(btn => {
                                btn.disabled = false;
                                btn.style.opacity = '1';
                            });

                            if (loginButton) {
                                loginButton.disabled = false;
                                if (buttonText) buttonText.textContent = '{{ __('تسجيل الدخول') }}';
                                if (loadingSpinner) loadingSpinner.classList.add('hidden');
                            }
                        }
                    }, 500);

                } catch (error) {
                    console.error('[LOGIN ERROR] Demo login failed:', error);

                    // Re-enable buttons on error
                    const demoButtons = document.querySelectorAll('#demoAdminLogin, #demoUserLogin');
                    demoButtons.forEach(btn => {
                        btn.disabled = false;
                        btn.style.opacity = '1';
                    });

                    // Show user-friendly error
                    alert('حدث خطأ في تسجيل الدخول التلقائي. يرجى المحاولة يدوياً.');
                }
            }

            // Demo Admin Button with Enhanced Debugging
            if (demoAdminButton) {
                debugLog('Setting up demo admin button event listener');
                debugLog('Demo admin button element:', demoAdminButton);
                debugLog('Demo admin button properties:', {
                    id: demoAdminButton.id,
                    tagName: demoAdminButton.tagName,
                    type: demoAdminButton.type,
                    disabled: demoAdminButton.disabled,
                    style: demoAdminButton.style.cssText
                });

                // Test if button is clickable
                demoAdminButton.addEventListener('mouseenter', function() {
                    debugLog('Demo admin button mouse enter detected');
                });

                demoAdminButton.addEventListener('click', function(e) {
                    debugLog('🚀 Demo admin button clicked - Event fired!');
                    e.preventDefault();
                    e.stopPropagation();
                    debugLog('Event prevented and stopped');
                    fillAndSubmitLogin('<EMAIL>', 'admin123', 'Admin');
                });

                // Alternative event listener for testing
                demoAdminButton.onclick = function(e) {
                    debugLog('🚀 Demo admin button onclick - Alternative event fired!');
                    e.preventDefault();
                    fillAndSubmitLogin('<EMAIL>', 'admin123', 'Admin');
                };

                debugLog('Demo admin button event listeners attached successfully');
            } else {
                console.warn('[LOGIN WARNING] Demo admin button not found');
            }

            // Demo User Button with Enhanced Debugging
            if (demoUserButton) {
                debugLog('Setting up demo user button event listener');
                debugLog('Demo user button element:', demoUserButton);
                debugLog('Demo user button properties:', {
                    id: demoUserButton.id,
                    tagName: demoUserButton.tagName,
                    type: demoUserButton.type,
                    disabled: demoUserButton.disabled,
                    style: demoUserButton.style.cssText
                });

                // Test if button is clickable
                demoUserButton.addEventListener('mouseenter', function() {
                    debugLog('Demo user button mouse enter detected');
                });

                demoUserButton.addEventListener('click', function(e) {
                    debugLog('🚀 Demo user button clicked - Event fired!');
                    e.preventDefault();
                    e.stopPropagation();
                    debugLog('Event prevented and stopped');
                    fillAndSubmitLogin('<EMAIL>', 'demo123', 'User');
                });

                // Alternative event listener for testing
                demoUserButton.onclick = function(e) {
                    debugLog('🚀 Demo user button onclick - Alternative event fired!');
                    e.preventDefault();
                    fillAndSubmitLogin('<EMAIL>', 'demo123', 'User');
                };

                debugLog('Demo user button event listeners attached successfully');
            } else {
                console.warn('[LOGIN WARNING] Demo user button not found');
            }

            // Additional debugging - Test button accessibility
            debugLog('Testing button accessibility...');
            setTimeout(() => {
                if (demoAdminButton) {
                    debugLog('Demo admin button still accessible after timeout');
                    debugLog('Button computed style:', window.getComputedStyle(demoAdminButton)
                        .pointerEvents);
                }
                if (demoUserButton) {
                    debugLog('Demo user button still accessible after timeout');
                    debugLog('Button computed style:', window.getComputedStyle(demoUserButton)
                        .pointerEvents);
                }
            }, 1000);

            debugLog('Login functionality initialization complete');

            // Expose test functions globally for manual testing
            window.testDemoLogin = function(type = 'admin') {
                debugLog(`Manual test function called for ${type}`);
                if (type === 'admin') {
                    fillAndSubmitLogin('<EMAIL>', 'admin123', 'Manual-Admin');
                } else {
                    fillAndSubmitLogin('<EMAIL>', 'demo123', 'Manual-User');
                }
            };

            window.testButtonClick = function() {
                debugLog('Manual button click test');
                const adminBtn = document.getElementById('demoAdminLogin');
                if (adminBtn) {
                    debugLog('Manually triggering admin button click');
                    adminBtn.click();
                } else {
                    console.error('Admin button not found for manual test');
                }
            };

            window.debugElements = function() {
                debugLog('=== MANUAL ELEMENT DEBUG ===');
                debugLog('Login Form:', document.getElementById('loginForm'));
                debugLog('Email Input:', document.getElementById('email'));
                debugLog('Password Input:', document.getElementById('password'));
                debugLog('Demo Admin Button:', document.getElementById('demoAdminLogin'));
                debugLog('Demo User Button:', document.getElementById('demoUserLogin'));
                debugLog('=== END DEBUG ===');
            };

            console.log('🧪 Test functions available:');
            console.log('- testDemoLogin("admin") or testDemoLogin("user")');
            console.log('- testButtonClick()');
            console.log('- debugElements()');
        });

        // Global error handler
        window.addEventListener('error', function(e) {
            console.error('[GLOBAL ERROR]', e.error);
        });

        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', function(e) {
            console.error('[UNHANDLED PROMISE REJECTION]', e.reason);
        });
    </script>
</x-guest-layout>
