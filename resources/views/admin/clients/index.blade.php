@extends('layouts.admin')

@section('title', 'العملاء')
@section('page-title', 'إدارة العملاء')

@section('content')
<div class="space-y-6">
    <!-- Header with Actions -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h2 class="text-xl font-semibold text-gray-900">إدارة العملاء</h2>
            <p class="mt-1 text-sm text-gray-600">إدارة وتنظيم قاعدة بيانات العملاء</p>
        </div>
        <div class="mt-4 sm:mt-0">
            <a href="{{ route('admin.clients.create') }}" class="admin-button">
                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                إضافة عميل جديد
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="admin-card">
        <form method="GET" action="{{ route('admin.clients.index') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <!-- Search -->
            <div>
                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">البحث</label>
                <input type="text" name="search" id="search" value="{{ request('search') }}" 
                       placeholder="البحث في العملاء..." 
                       class="admin-input w-full">
            </div>

            <!-- Industry Filter -->
            <div>
                <label for="industry_type" class="block text-sm font-medium text-gray-700 mb-1">نوع الصناعة</label>
                <select name="industry_type" id="industry_type" class="admin-select w-full">
                    <option value="">جميع الصناعات</option>
                    @foreach($industries as $key => $value)
                        <option value="{{ $key }}" {{ request('industry_type') == $key ? 'selected' : '' }}>
                            {{ $value }}
                        </option>
                    @endforeach
                </select>
            </div>

            <!-- Status Filter -->
            <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">الحالة</label>
                <select name="status" id="status" class="admin-select w-full">
                    <option value="">جميع الحالات</option>
                    <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>نشط</option>
                    <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>غير نشط</option>
                    <option value="potential" {{ request('status') == 'potential' ? 'selected' : '' }}>عميل محتمل</option>
                    <option value="archived" {{ request('status') == 'archived' ? 'selected' : '' }}>مؤرشف</option>
                </select>
            </div>

            <!-- Actions -->
            <div class="flex items-end space-x-2 space-x-reverse">
                <button type="submit" class="admin-button">
                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    بحث
                </button>
                <a href="{{ route('admin.clients.index') }}" class="admin-button-secondary">
                    إعادة تعيين
                </a>
            </div>
        </form>
    </div>

    <!-- Clients Table -->
    <div class="admin-card">
        @if($clients->count() > 0)
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                العميل
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                نوع الصناعة
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                المشاريع
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                القيمة الإجمالية
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                الحالة
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                الإجراءات
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($clients as $client)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ $client->company_name }}</div>
                                        <div class="text-sm text-gray-500">{{ $client->contact_person }}</div>
                                        <div class="text-sm text-gray-500">{{ $client->email }}</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="text-sm text-gray-900">{{ $client->industry_name }}</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        <div>إجمالي: {{ $client->projects_count }}</div>
                                        <div class="text-xs text-gray-500">
                                            نشط: {{ $client->active_projects_count }} | 
                                            مكتمل: {{ $client->completed_projects_count }}
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="text-sm font-medium text-gray-900">
                                        {{ number_format($client->total_value, 0) }} ريال
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="status-badge 
                                        @if($client->status === 'active') status-active
                                        @elseif($client->status === 'inactive') status-inactive
                                        @elseif($client->status === 'potential') status-pending
                                        @else status-inactive
                                        @endif">
                                        {{ $client->status_name }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <a href="{{ route('admin.clients.show', $client) }}" 
                                           class="text-primary-600 hover:text-primary-900">
                                            عرض
                                        </a>
                                        <a href="{{ route('admin.clients.edit', $client) }}" 
                                           class="text-yellow-600 hover:text-yellow-900">
                                            تعديل
                                        </a>
                                        <form action="{{ route('admin.clients.destroy', $client) }}" 
                                              method="POST" 
                                              class="inline"
                                              onsubmit="return confirm('هل أنت متأكد من حذف هذا العميل؟')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="text-red-600 hover:text-red-900">
                                                حذف
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="mt-6">
                {{ $clients->withQueryString()->links() }}
            </div>
        @else
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">لا توجد عملاء</h3>
                <p class="mt-1 text-sm text-gray-500">ابدأ بإضافة عميل جديد.</p>
                <div class="mt-6">
                    <a href="{{ route('admin.clients.create') }}" class="admin-button">
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        إضافة عميل جديد
                    </a>
                </div>
            </div>
        @endif
    </div>
</div>
@endsection
