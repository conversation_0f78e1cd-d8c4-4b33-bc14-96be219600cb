<?php

// Simple script to create admin users
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use Illuminate\Support\Facades\Hash;

try {
    // Create admin user
    $admin = User::firstOrCreate(
        ['email' => '<EMAIL>'],
        [
            'name' => 'مدير النظام',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'role' => 'admin',
            'email_verified_at' => now(),
        ]
    );

    // Create demo user
    $demo = User::firstOrCreate(
        ['email' => '<EMAIL>'],
        [
            'name' => 'مستخدم تجريبي',
            'email' => '<EMAIL>',
            'password' => Hash::make('demo123'),
            'role' => 'user',
            'email_verified_at' => now(),
        ]
    );

    echo "✅ Admin users created successfully!\n";
    echo "📧 Admin: <EMAIL> / admin123\n";
    echo "📧 Demo: <EMAIL> / demo123\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
