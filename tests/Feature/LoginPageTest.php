<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class LoginPageTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that the login page loads successfully.
     */
    public function test_login_page_loads_successfully(): void
    {
        $response = $this->get('/login');

        $response->assertStatus(200);
        $response->assertViewIs('auth.login');
    }

    /**
     * Test that the login page contains required elements.
     */
    public function test_login_page_contains_required_elements(): void
    {
        $response = $this->get('/login');

        $response->assertStatus(200);
        
        // Check for form elements
        $response->assertSee('تسجيل الدخول'); // Login title in Arabic
        $response->assertSee('البريد الإلكتروني'); // Email label in Arabic
        $response->assertSee('كلمة المرور'); // Password label in Arabic
        $response->assertSee('تذكرني'); // Remember me in Arabic
        $response->assertSee('نسيت كلمة المرور؟'); // Forgot password in Arabic
        
        // Check for form structure
        $response->assertSee('name="email"', false);
        $response->assertSee('name="password"', false);
        $response->assertSee('name="remember"', false);
        $response->assertSee('type="submit"', false);
    }

    /**
     * Test that the login page has proper RTL support.
     */
    public function test_login_page_has_rtl_support(): void
    {
        $response = $this->get('/login');

        $response->assertStatus(200);
        
        // Check for RTL direction
        $response->assertSee('dir="rtl"', false);
        
        // Check for Arabic font
        $response->assertSee('IBM Plex Sans Arabic', false);
    }

    /**
     * Test that the login page includes required CSS and JS assets.
     */
    public function test_login_page_includes_assets(): void
    {
        $response = $this->get('/login');

        $response->assertStatus(200);
        
        // Check for Vite assets
        $response->assertSee('resources/css/app.css', false);
        $response->assertSee('resources/js/app.js', false);
        $response->assertSee('resources/js/auth.js', false);
    }

    /**
     * Test that the login page has proper responsive design classes.
     */
    public function test_login_page_has_responsive_design(): void
    {
        $response = $this->get('/login');

        $response->assertStatus(200);
        
        // Check for responsive classes
        $response->assertSee('lg:w-1/2', false); // Split screen on large screens
        $response->assertSee('lg:hidden', false); // Mobile-specific elements
        $response->assertSee('min-h-screen', false); // Full height
    }

    /**
     * Test that the login page has accessibility features.
     */
    public function test_login_page_has_accessibility_features(): void
    {
        $response = $this->get('/login');

        $response->assertStatus(200);
        
        // Check for accessibility attributes
        $response->assertSee('autofocus', false);
        $response->assertSee('required', false);
        $response->assertSee('autocomplete', false);
        
        // Check for proper labeling
        $response->assertSee('for="email"', false);
        $response->assertSee('for="password"', false);
        $response->assertSee('for="remember_me"', false);
    }

    /**
     * Test that the login page has modern UI elements.
     */
    public function test_login_page_has_modern_ui_elements(): void
    {
        $response = $this->get('/login');

        $response->assertStatus(200);
        
        // Check for modern design classes
        $response->assertSee('rounded-xl', false); // Rounded corners
        $response->assertSee('shadow-xl', false); // Shadows
        $response->assertSee('gradient', false); // Gradients
        $response->assertSee('transition', false); // Transitions
        
        // Check for interactive elements
        $response->assertSee('togglePassword', false); // Password toggle
        $response->assertSee('loadingSpinner', false); // Loading spinner
    }

    /**
     * Test that the login page has proper form validation structure.
     */
    public function test_login_page_has_form_validation_structure(): void
    {
        $response = $this->get('/login');

        $response->assertStatus(200);
        
        // Check for CSRF protection
        $response->assertSee('name="_token"', false);
        
        // Check for error display components
        $response->assertSee('x-input-error', false);
        
        // Check for session status component
        $response->assertSee('x-auth-session-status', false);
    }
}
