<?php $__env->startSection('title', 'لوحة التحكم'); ?>
<?php $__env->startSection('page-title', 'لوحة التحكم'); ?>

<?php $__env->startSection('content'); ?>
    <div class="space-y-4 sm:space-y-6">
        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
            <!-- Total Clients -->
            <div class="admin-card">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z">
                                </path>
                            </svg>
                        </div>
                    </div>
                    <div class="mr-3 sm:mr-4">
                        <p class="text-xs sm:text-sm font-medium text-gray-600">إجمالي العملاء</p>
                        <p class="text-xl sm:text-2xl font-semibold text-gray-900"><?php echo e($stats['total_clients']); ?></p>
                        <p class="text-xs text-green-600"><?php echo e($stats['active_clients']); ?> نشط</p>
                    </div>
                </div>
            </div>

            <!-- Total Projects -->
            <div class="admin-card">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10">
                                </path>
                            </svg>
                        </div>
                    </div>
                    <div class="mr-4">
                        <p class="text-sm font-medium text-gray-600">إجمالي المشاريع</p>
                        <p class="text-2xl font-semibold text-gray-900"><?php echo e($stats['total_projects']); ?></p>
                        <p class="text-xs text-blue-600"><?php echo e($stats['active_projects']); ?> نشط</p>
                    </div>
                </div>
            </div>

            <!-- Completed Projects -->
            <div class="admin-card">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="mr-4">
                        <p class="text-sm font-medium text-gray-600">المشاريع المكتملة</p>
                        <p class="text-2xl font-semibold text-gray-900"><?php echo e($stats['completed_projects']); ?></p>
                        <p class="text-xs text-purple-600">مكتمل</p>
                    </div>
                </div>
            </div>

            <!-- Monthly Revenue -->
            <div class="admin-card">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
                                </path>
                            </svg>
                        </div>
                    </div>
                    <div class="mr-4">
                        <p class="text-sm font-medium text-gray-600">الإيرادات الشهرية</p>
                        <p class="text-2xl font-semibold text-gray-900"><?php echo e(number_format($monthly_revenue, 0)); ?></p>
                        <p class="text-xs text-yellow-600">ريال سعودي</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Projects and Clients -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
            <!-- Recent Projects -->
            <div class="admin-card">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-base sm:text-lg font-medium text-gray-900">المشاريع الحديثة</h3>
                    <a href="<?php echo e(route('admin.projects.index')); ?>"
                        class="text-xs sm:text-sm text-primary-600 hover:text-primary-500">
                        عرض الكل
                    </a>
                </div>
                <div class="space-y-3">
                    <?php $__empty_1 = true; $__currentLoopData = $recent_projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div
                            class="flex flex-col sm:flex-row sm:items-center justify-between p-3 bg-gray-50 rounded-lg space-y-2 sm:space-y-0">
                            <div class="flex-1">
                                <p class="font-medium text-gray-900 text-sm sm:text-base"><?php echo e($project->name); ?></p>
                                <p class="text-xs sm:text-sm text-gray-600"><?php echo e($project->client->company_name); ?></p>
                            </div>
                            <div class="text-right sm:text-left">
                                <span
                                    class="status-badge text-xs
                                <?php if($project->status === 'completed'): ?> status-completed
                                <?php elseif($project->status === 'in_progress'): ?> status-active
                                <?php elseif($project->status === 'planning'): ?> status-pending
                                <?php else: ?> status-inactive <?php endif; ?>">
                                    <?php echo e($project->status_name); ?>

                                </span>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <p class="text-gray-500 text-center py-4">لا توجد مشاريع حديثة</p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Clients -->
            <div class="admin-card">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-base sm:text-lg font-medium text-gray-900">العملاء الجدد</h3>
                    <a href="<?php echo e(route('admin.clients.index')); ?>"
                        class="text-xs sm:text-sm text-primary-600 hover:text-primary-500">
                        عرض الكل
                    </a>
                </div>
                <div class="space-y-3">
                    <?php $__empty_1 = true; $__currentLoopData = $recent_clients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $client): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div
                            class="flex flex-col sm:flex-row sm:items-center justify-between p-3 bg-gray-50 rounded-lg space-y-2 sm:space-y-0">
                            <div class="flex-1">
                                <p class="font-medium text-gray-900 text-sm sm:text-base"><?php echo e($client->company_name); ?></p>
                                <p class="text-xs sm:text-sm text-gray-600"><?php echo e($client->contact_person); ?></p>
                            </div>
                            <div class="text-right sm:text-left">
                                <span
                                    class="status-badge text-xs
                                <?php if($client->status === 'active'): ?> status-active
                                <?php elseif($client->status === 'potential'): ?> status-pending
                                <?php else: ?> status-inactive <?php endif; ?>">
                                    <?php echo e($client->status_name); ?>

                                </span>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <p class="text-gray-500 text-center py-4">لا توجد عملاء جدد</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="admin-card">
            <h3 class="text-base sm:text-lg font-medium text-gray-900 mb-4">إجراءات سريعة</h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                <a href="<?php echo e(route('admin.projects.create')); ?>"
                    class="flex items-center p-3 sm:p-4 bg-primary-50 rounded-lg hover:bg-primary-100 transition-colors duration-200">
                    <div class="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center ml-3 flex-shrink-0">
                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="font-medium text-primary-900 text-sm sm:text-base">إضافة مشروع جديد</p>
                        <p class="text-xs sm:text-sm text-primary-600">إنشاء مشروع جديد للعملاء</p>
                    </div>
                </a>

                <a href="<?php echo e(route('admin.clients.create')); ?>"
                    class="flex items-center p-3 sm:p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors duration-200">
                    <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center ml-3 flex-shrink-0">
                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="font-medium text-green-900 text-sm sm:text-base">إضافة عميل جديد</p>
                        <p class="text-xs sm:text-sm text-green-600">تسجيل عميل جديد في النظام</p>
                    </div>
                </a>

                <a href="<?php echo e(route('admin.services.create')); ?>"
                    class="flex items-center p-3 sm:p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors duration-200">
                    <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center ml-3 flex-shrink-0">
                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="font-medium text-purple-900 text-sm sm:text-base">إضافة خدمة جديدة</p>
                        <p class="text-xs sm:text-sm text-purple-600">إضافة خدمة تقنية جديدة</p>
                    </div>
                </a>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Sites/latestlial/resources/views/admin/dashboard.blade.php ENDPATH**/ ?>