document.addEventListener("DOMContentLoaded",function(){r(),u(),m(),f(),p()});function r(){document.querySelectorAll(".floating-label-input").forEach(e=>{e.value&&e.classList.add("has-value"),e.addEventListener("focus",function(){this.classList.add("focused")}),e.addEventListener("blur",function(){this.classList.remove("focused"),this.value?this.classList.add("has-value"):this.classList.remove("has-value")}),e.addEventListener("input",function(){this.value?this.classList.add("has-value"):this.classList.remove("has-value")})})}function u(){const t=document.getElementById("togglePassword"),e=document.getElementById("password"),n=document.getElementById("eyeIcon"),s=document.getElementById("eyeOffIcon");t&&e&&t.addEventListener("click",function(){const o=e.type==="password";e.type=o?"text":"password",o?(n.style.opacity="0",setTimeout(()=>{n.classList.add("hidden"),s.classList.remove("hidden"),s.style.opacity="1"},150)):(s.style.opacity="0",setTimeout(()=>{s.classList.add("hidden"),n.classList.remove("hidden"),n.style.opacity="1"},150)),v(t)})}function m(){const t=document.getElementById("loginForm"),e=document.getElementById("email"),n=document.getElementById("password");t&&(e==null||e.addEventListener("blur",i),n==null||n.addEventListener("blur",a),t.addEventListener("submit",function(s){let o=!0;i()||(o=!1),a()||(o=!1),o||(s.preventDefault(),h())}))}function i(){const t=document.getElementById("email"),e=/^[^\s@]+@[^\s@]+\.[^\s@]+$/;if(t&&t.value){const n=e.test(t.value);return l(t,!n,"يرجى إدخال بريد إلكتروني صحيح"),n}return!0}function a(){const t=document.getElementById("password");if(t&&t.value){const e=t.value.length>=6;return l(t,!e,"كلمة المرور يجب أن تكون 6 أحرف على الأقل"),e}return!0}function l(t,e,n){const s=t.parentNode.nextElementSibling;e?(t.classList.add("input-error"),s&&!s.textContent&&(s.innerHTML=`
                <div class="flex items-center space-x-2 space-x-reverse text-red-600 text-sm">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>${n}</span>
                </div>
            `)):(t.classList.remove("input-error"),s&&(s.innerHTML=""))}function f(){const t=document.getElementById("loginForm"),e=document.getElementById("loginButton"),n=document.getElementById("buttonText"),s=document.getElementById("loadingSpinner");t&&e&&t.addEventListener("submit",function(){t.querySelectorAll("input, button").forEach(d=>d.disabled=!0),e.classList.add("loading"),n.textContent="جاري تسجيل الدخول...",s==null||s.classList.remove("hidden"),e.classList.add("pulse-slow")})}function p(){document.querySelectorAll(".space-y-6 > div, .space-y-6 > button").forEach((e,n)=>{e.style.animationDelay=`${n*.1}s`,e.classList.add("fade-in")}),y()}function y(){document.querySelectorAll('button, a, input[type="checkbox"]').forEach(e=>{e.addEventListener("mouseenter",function(){this.style.transform="scale(1.02)"}),e.addEventListener("mouseleave",function(){this.style.transform="scale(1)"})})}function v(t){const e=document.createElement("span"),n=t.getBoundingClientRect(),s=Math.max(n.width,n.height);e.style.width=e.style.height=s+"px",e.style.left=event.clientX-n.left-s/2+"px",e.style.top=event.clientY-n.top-s/2+"px",e.classList.add("ripple"),t.appendChild(e),setTimeout(()=>{e.remove()},600)}function h(){const t=document.getElementById("loginForm");t&&(t.style.animation="shake 0.5s ease-in-out",setTimeout(()=>{t.style.animation=""},500))}document.addEventListener("keydown",function(t){if(t.key==="Enter"&&t.target.type==="checkbox"&&(t.target.checked=!t.target.checked),t.key==="Escape"){const e=document.getElementById("loginForm");e&&confirm("هل تريد مسح النموذج؟")&&(e.reset(),e.querySelectorAll(".floating-label-input").forEach(s=>{s.classList.remove("has-value","focused")}))}});const g=`
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
        20%, 40%, 60%, 80% { transform: translateX(5px); }
    }
    
    .loading {
        pointer-events: none;
    }
`,c=document.createElement("style");c.textContent=g;document.head.appendChild(c);
