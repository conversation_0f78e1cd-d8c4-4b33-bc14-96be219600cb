<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSRF Test Page</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; direction: rtl; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        input { padding: 10px; margin: 5px; border: 2px solid #ddd; border-radius: 5px; width: 300px; }
        #results { background: #f8f9fa; padding: 10px; font-family: monospace; height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>🔍 CSRF Token Test Page</h1>
    
    <div class="test-section info">
        <h3>Environment Info</h3>
        <p><strong>URL:</strong> <span id="current-url"></span></p>
        <p><strong>Cookies:</strong> <span id="cookies-info"></span></p>
        <p><strong>User Agent:</strong> <span id="user-agent"></span></p>
    </div>

    <div class="test-section">
        <h3>🧪 CSRF Token Tests</h3>
        <button onclick="testGetCSRF()">1. Get CSRF Token</button>
        <button onclick="testPostCSRF()">2. Test POST with CSRF</button>
        <button onclick="testLoginForm()">3. Test Login Form</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <div class="test-section">
        <h3>📊 Test Results</h3>
        <div id="results"></div>
    </div>

    <script>
        // Initialize page info
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('cookies-info').textContent = document.cookie || 'No cookies';
        document.getElementById('user-agent').textContent = navigator.userAgent;

        const results = document.getElementById('results');

        function addResult(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const result = document.createElement('div');
            result.innerHTML = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            result.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'blue';
            results.appendChild(result);
            results.scrollTop = results.scrollHeight;
        }

        function clearResults() {
            results.innerHTML = '';
        }

        async function testGetCSRF() {
            addResult('Testing CSRF token retrieval...');
            try {
                const response = await fetch('http://latestlial.test/test-csrf', {
                    method: 'GET',
                    credentials: 'include'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`CSRF Token: ${data.csrf_token.substring(0, 20)}...`, 'success');
                    addResult(`Session ID: ${data.session_id}`, 'info');
                    addResult(`Session Driver: ${data.session_driver}`, 'info');
                    addResult(`Session Domain: ${data.session_domain || 'null'}`, 'info');
                    addResult(`Cookies: ${JSON.stringify(data.cookies)}`, 'info');
                } else {
                    addResult(`Failed to get CSRF token: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`Error getting CSRF token: ${error.message}`, 'error');
            }
        }

        async function testPostCSRF() {
            addResult('Testing POST with CSRF token...');
            try {
                // First get the CSRF token
                const getResponse = await fetch('http://latestlial.test/test-csrf', {
                    method: 'GET',
                    credentials: 'include'
                });
                
                if (!getResponse.ok) {
                    addResult('Failed to get CSRF token for POST test', 'error');
                    return;
                }
                
                const tokenData = await getResponse.json();
                const csrfToken = tokenData.csrf_token;
                
                addResult(`Using CSRF token: ${csrfToken.substring(0, 20)}...`);
                
                // Now test POST with the token
                const formData = new FormData();
                formData.append('_token', csrfToken);
                formData.append('test_data', 'Hello World');
                
                const postResponse = await fetch('http://latestlial.test/test-csrf-post', {
                    method: 'POST',
                    body: formData,
                    credentials: 'include'
                });
                
                if (postResponse.ok) {
                    const result = await postResponse.json();
                    addResult(`POST test successful: ${result.message}`, 'success');
                    addResult(`Token received: ${result.token_received.substring(0, 20)}...`, 'info');
                } else {
                    const errorText = await postResponse.text();
                    addResult(`POST test failed: ${postResponse.status} - ${errorText}`, 'error');
                }
            } catch (error) {
                addResult(`Error in POST test: ${error.message}`, 'error');
            }
        }

        async function testLoginForm() {
            addResult('Testing login form submission...');
            try {
                // Get login page
                const loginResponse = await fetch('http://latestlial.test/login', {
                    method: 'GET',
                    credentials: 'include'
                });
                
                if (!loginResponse.ok) {
                    addResult('Failed to get login page', 'error');
                    return;
                }
                
                const loginHtml = await loginResponse.text();
                
                // Extract CSRF token from login page
                const tokenMatch = loginHtml.match(/name="_token"[^>]*value="([^"]*)"/) || 
                                 loginHtml.match(/content="([^"]*)"[^>]*name="csrf-token"/);
                
                if (!tokenMatch) {
                    addResult('No CSRF token found in login page', 'error');
                    return;
                }
                
                const csrfToken = tokenMatch[1];
                addResult(`Found CSRF token in login page: ${csrfToken.substring(0, 20)}...`);
                
                // Test login submission
                const formData = new FormData();
                formData.append('_token', csrfToken);
                formData.append('email', '<EMAIL>');
                formData.append('password', 'admin123');
                
                const loginSubmitResponse = await fetch('http://latestlial.test/login', {
                    method: 'POST',
                    body: formData,
                    credentials: 'include',
                    redirect: 'manual'
                });
                
                addResult(`Login response status: ${loginSubmitResponse.status}`);
                
                if (loginSubmitResponse.status === 302) {
                    const location = loginSubmitResponse.headers.get('Location');
                    addResult(`Login successful! Redirecting to: ${location}`, 'success');
                } else if (loginSubmitResponse.status === 419) {
                    addResult('419 Page Expired - CSRF token validation failed', 'error');
                } else {
                    const responseText = await loginSubmitResponse.text();
                    addResult(`Unexpected response: ${responseText.substring(0, 200)}...`, 'error');
                }
                
            } catch (error) {
                addResult(`Error in login test: ${error.message}`, 'error');
            }
        }

        // Auto-run initial test
        window.addEventListener('load', function() {
            addResult('CSRF Test Page loaded successfully', 'success');
            addResult('Click buttons above to run tests', 'info');
        });
    </script>
</body>
</html>
